{"user_preferences": {"development_strategy": {"prioritize_refactor_and_fix": true, "analyze_implementation_reports_before_features": true, "avoid_code_duplication_in_scraping": true, "comprehensive_refactor_goals": ["consolidate_package_management", "improve_architecture", "eliminate_code_duplication", "consistent_coding_standards", "optimize_dependencies", "modern_js_ts_best_practices"], "refactor_phases": ["analysis_assessment", "package_management_consolidation", "architecture_improvement_with_scraper_interfaces", "code_quality_80pct_coverage", "security_performance_updates"], "require_analysis_reports_and_migration_guides": true}, "supabase": {"use_local_instance": true, "local_instance_path": "/home/<USER>/GithubProjects/supabase", "port": 8000}, "documentation": {"keep_readme_changelog_updated": true, "reflect_project_structure_and_features": true, "reflect_db_schema_changes_from": "scripts/db/"}, "testing": {"frameworks": ["Jest", "React Testing Library"], "strategy": ["component_testing", "imports_exports", "error_handling", "integration_testing", "environment_setup"], "debug_and_fix_test_errors": true, "test_coverage_requirement_pct": 80}, "logging": {"library": "<PERSON>", "configuration": "specific"}, "performance": {"optimize_dynamic_imports": true, "code_splitting": true, "target_bundle_size_kb": 250}, "mcp": {"use_filesystem_server": true, "server_repo": "modelcontextprotocol/servers", "setup_tutorial": "Augment Code", "documentation_url": "https://docs.augmentcode.com/setup-augment/mcp"}, "scraping": {"avoid_code_duplication": true, "ensure_db_update_through_scraping": true, "data_source_dynamic_filtering": {"date_filtering": true, "configurable_price_range": true, "applies_to": ["Revolico.com"]}}, "dependency_management": {"update_puppeteer": ">=22.8.2", "replace_outdated_dependencies": ["inflight", "<PERSON><PERSON><PERSON>", "glob"], "update_vulnerable_npm_packages": ["@babel/runtime", "esbuild", "nanoid", "request", "tough-cookie"], "note_breaking_changes": true}, "ui": {"fix_visibility_issues": true, "refactor_for_code_quality_and_ts": true}, "todo": {"sections": ["current_priority_tasks", "technical_debt_refactoring", "dependency_updates", "database_api_integration", "testing_qa", "performance_optimization", "documentation_updates"], "priority_levels": ["High", "Medium", "Low"], "estimated_effort": true, "checkboxes_for_tracking": true}}, "database_schema": {"refined_supabase_schema": true, "normalized_tables": ["laptop_specifications", "components", "llm_compatibility", "pricing"], "optimize_project": true, "ensure_compatibility_with_sources": ["Revolico.com", "laptop-ventas.ola.click", "Smart-things.ola.click"], "store_scraped_data_properly": true, "location": "scripts/db/"}, "current_tasks_and_issues": {"recent_fixes": ["LaptopSpecsCard component", "Supabase test configuration", "failed tests", "export problems"], "remaining_issues": ["Jest configuration", "component mocking"], "searches": [{"id": "841558eb-da0c-4667-860c-4dfc67304eb1", "found": false}]}, "supabase_configuration": {"local_installation_path": "/home/<USER>/GithubProjects/supabase/"}, "scraping_implementation": {"revolico_refactor": {"uses_graphql_api": true, "specialized_scraper_class": "RevolicoScraper", "updated_type_system": true, "db_schema_changes": true, "dynamic_config_system": true}, "python_implementation": {"scrapes_revolico_using_graphql": true}}}