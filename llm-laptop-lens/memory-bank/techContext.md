# Contexto Técnico: Pila Tecnológica y Dependencias

Este documento detalla las tecnologías, librerías y herramientas utilizadas en la refactorización de los scripts del proyecto.

## Pila Tecnológica Principal

*   **Node.js**: El entorno de ejecución para todos los scripts. Se utilizará la sintaxis de Módulos ES (`import`/`export`).
*   **PostgreSQL**: La base de datos utilizada por el proyecto, gestionada a través de Supabase en un contenedor Docker para el desarrollo local.
*   **Docker**: Utilizado para orquestar el entorno de desarrollo local de Supabase, garantizando la consistencia entre diferentes máquinas.

## Dependencias de Desarrollo (`devDependencies`)

Estas son las librerías clave que se instalarán para construir la nueva herramienta CLI.

*   ### **`commander`**
    *   **Propósito**: Es la librería fundamental para construir la interfaz de línea de comandos. Permite definir comandos, subcomandos, opciones y generar automáticamente menús de ayuda.
    *   **Uso**: Se utilizará en `scripts/cli.mjs` para orquestar toda la aplicación.

*   ### **`postgres`**
    *   **Propósito**: Un cliente de PostgreSQL para Node.js de alto rendimiento y sin dependencias. Es la pieza clave para eliminar la frágil dependencia de `docker exec psql`.
    *   **Uso**: Se encapsulará dentro de `scripts/lib/db-client.mjs` para gestionar todas las conexiones y consultas a la base de datos.

*   ### **`chalk`**
    *   **Propósito**: Librería para estilizar la salida de la terminal con colores. Es esencial para proporcionar feedback visual claro al usuario (éxitos en verde, errores en rojo, advertencias en amarillo).
    *   **Uso**: Se abstraerá en `scripts/utils/logger.mjs` para crear funciones de log estandarizadas.

*   ### **`dotenv`**
    *   **Propósito**: Carga variables de entorno desde un archivo `.env` a `process.env`.
    *   **Uso**: Se utilizará en la capa de configuración (`scripts/config/`) para cargar las credenciales de la base de datos y otras configuraciones sensibles de forma segura.

## Entorno de Desarrollo

*   **Gestor de Paquetes**: `npm` se utilizará para gestionar las dependencias del proyecto.
*   **Control de Versiones**: `git` se utiliza para el control de versiones del código fuente.
*   **Archivo de Configuración**: Un archivo `.env` en la raíz del proyecto almacenará las variables de entorno necesarias, como `SUPABASE_URL` y `SUPABASE_SERVICE_KEY`.

## Restricciones y Consideraciones Técnicas

*   **Módulos ES (ESM)**: Todo el código nuevo se escribirá utilizando la sintaxis de Módulos ES. Esto implica el uso de `import`/`export` y la gestión de rutas de archivo con `import.meta.url`.
*   **Conexión de Red a Docker**: La nueva implementación requiere que el contenedor de Docker de Supabase exponga el puerto de PostgreSQL (normalmente el `5432`) a la máquina anfitriona (`localhost`) para que el cliente `postgres` de Node.js pueda conectarse.
*   **Orden de Migraciones**: El sistema de migración dependerá de una convención de nombrado de archivos (ej. `0001_...`, `0002_...`) para determinar el orden de ejecución. Es crucial mantener esta convención.
