# Contexto Activo: Inicialización y Planificación

**Fecha:** 15 de Julio de 2025

## Foco de Trabajo Actual

El foco actual es la **planificación y el diseño** de la refactorización completa de los scripts de utilidad del proyecto. Se ha completado el análisis de los scripts existentes y se ha formulado un plan de acción detallado.

## Cambios Recientes

*   Se ha creado el `MemoryBank` para documentar el proyecto.
*   Se han creado los siguientes documentos de `MemoryBank`:
    *   `projectbrief.md`
    *   `productContext.md`
    *   `systemPatterns.md`
    *   `techContext.md`
*   Se ha definido un plan de refactorización completo que abarca todos los scripts en `scripts/` y `scripts/db/`.

## Próximos Pasos

1.  **Crear el archivo `progress.md`**: Completar la inicialización del `MemoryBank`.
2.  **Presentar el Plan al Usuario**: Comunicar el plan de refactorización completo y unificado al usuario para su aprobación.
3.  **Esperar Aprobación y Cambio a "Act Mode"**: El plan no se puede implementar hasta que el usuario dé su aprobación y cambie el modo de operación.
4.  **Fase 1 de Implementación (una vez en "Act Mode")**:
    *   Instalar las dependencias de desarrollo necesarias (`commander`, `postgres`, `chalk`, `dotenv`).
    *   Crear la estructura de directorios `scripts/utils/` y `scripts/config/`.
    *   Implementar los módulos de utilidad iniciales: `paths.mjs`, `logger.mjs`, `shell.mjs`.
    *   Implementar el archivo de configuración de la base de datos: `db.config.mjs`.

## Decisiones y Consideraciones Activas

*   **Herramienta CLI Unificada**: Se ha tomado la decisión de consolidar todos los scripts en una única herramienta CLI en lugar de refactorizarlos como scripts independientes. Esto mejora drásticamente la experiencia del desarrollador.
*   **Abstracción de la Base de Datos**: La decisión más crítica es abandonar `docker exec` en favor de un cliente de base de datos de red (`postgres`). Esto es fundamental para la robustez y portabilidad del sistema.
*   **Convención de Migraciones**: Se ha decidido adoptar una convención de nombrado de archivos basada en prefijos numéricos para gestionar el orden de las migraciones de la base de datos, lo cual es un patrón estándar y fiable.
