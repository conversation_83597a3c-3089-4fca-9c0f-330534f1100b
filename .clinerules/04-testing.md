# 04 - Pruebas

## Estrategias de Pruebas

*   **Pruebas Unitarias**: Escribir pruebas unitarias para funciones puras, lógica de negocio y componentes aislados.
*   **Pruebas de Integración**: Realizar pruebas de integración para asegurar que diferentes módulos o servicios interactúen correctamente (ej. integración con APIs, base de datos).
*   **Pruebas End-to-End (E2E)**: Implementar pruebas E2E para validar flujos de usuario críticos a través de la interfaz de usuario.

## Cobertura de Código

*   **Objetivos de Cobertura**: Establecer objetivos realistas de cobertura de código (ej. 80% para lógica de negocio, 60% para componentes UI).
*   **Herramientas de Cobertura**: Utilizar herramientas como Vitest para medir la cobertura de código.

## Uso de Mocks y Stubs

*   **Mocks para Dependencias Externas**: Usar mocks para simular el comportamiento de APIs externas, bases de datos o servicios de terceros en pruebas unitarias y de integración.
*   **Stubs para Datos de Prueba**: Crear stubs para datos de prueba consistentes y predecibles.

## Imprescindible:

*   **Vitest + Testing Library setup**
*   **Unit tests (80%+ coverage)**
*   **Integration tests para flujos críticos**
*   **E2E tests con Playwright**
*   **Mock strategies (MSW para APIs)**
*   **Test-driven development guidelines**
