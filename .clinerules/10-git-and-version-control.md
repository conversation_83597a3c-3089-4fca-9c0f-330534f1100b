# 10 - Git y Control de Versiones

## Convenciones de Commits

*   **Conventional Commits**: Seguir la especificación de Conventional Commits para mensajes de commit claros y automatizables (ej. `feat: add new feature`, `fix: resolve bug`).
*   **Atomic Commits**: Realizar commits pequeños y atómicos que representen un único cambio lógico.

## Convenciones de Nomenclatura de Ramas

*   **Ramas de Características**: Usar `feature/nombre-de-la-caracteristica` (ej. `feature/user-auth`, `feature/product-page`).
*   **Ramas de Corrección de Errores**: Usar `fix/descripcion-del-bug` (ej. `fix/login-issue`, `fix/data-display`).
*   **Ramas de Lanzamiento**: Usar `release/version` (ej. `release/1.0.0`).
*   **Ramas de Hotfix**: Usar `hotfix/descripcion-del-hotfix`.

## Plantillas de Pull Request (PR)

*   **Uso de Plantillas**: Implementar plantillas de PR para asegurar que todas las solicitudes de extracción contengan la información necesaria (ej. descripción, cambios, pruebas, capturas de pantalla).
*   **Revisión de Código**: Fomentar la revisión de código por pares y establecer criterios claros para la aprobación de PRs.

## Git Hooks

*   **Husky**: Utilizar Husky para automatizar tareas antes de los commits o pushes (ej. linting, formateo, pruebas unitarias).
*   **Pre-commit hooks**: Ejecutar linters y formatters para asegurar la calidad del código antes de cada commit.
*   **Pre-push hooks**: Ejecutar pruebas unitarias o de integración antes de cada push para evitar subir código roto.
