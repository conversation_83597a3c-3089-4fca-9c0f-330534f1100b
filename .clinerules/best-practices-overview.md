# Overview de Mejores Prácticas de Ingeniería de Software

Este directorio `.clinerules/` contiene un conjunto de directrices y mejores prácticas para el desarrollo de software en este proyecto. Estas reglas están diseñadas para asegurar la calidad, consistencia, mantenibilidad y escalabilidad del código.

Cada archivo Markdown en este directorio aborda una categoría específica de mejores prácticas.

## Categorías de Reglas:

*   **01-code-quality-and-style.md**: Estándares de codificación, convenciones de nomenclatura, formato y legibilidad.
*   **02-architecture-and-component-structure.md**: Organización de directorios, patrones de componentes y manejo de estado.
*   **03-data-and-api-handling.md**: Estrategias de fetching de datos, validación y manejo de errores.
*   **04-testing.md**: Estrategias de pruebas, cobertura de código y uso de mocks.
*   **05-documentation.md**: Directrices para la documentación en código y el uso del Memory Bank.
*   **06-performance-and-optimization.md**: Técnicas para mejorar el rendimiento y la optimización.
*   **07-security.md**: Mejores prácticas de seguridad, manejo de credenciales y prevención de vulnerabilidades.
*   **08-ui-ux-design.md**: Diseño y experiencia de usuario (UI/UX).
*   **09-deployment-and-devops.md**: CI/CD pipelines, gestión de entornos, monitoreo y logging, y estrategias de rollback.
*   **10-git-and-version-control.md**: Convenciones de commits, nomenclatura de ramas, plantillas de Pull Request y Git Hooks.
*   **11-dependency-management.md**: Mantenimiento de `package.json`, auditorías de seguridad, estrategias de actualización y cumplimiento de licencias.

Se recomienda revisar estos documentos regularmente para asegurar la adherencia a las directrices del proyecto.

## Workflows Disponibles

- `/deploy` - Deployment automático
- `/setup-feature` - Crear nueva feature
- `/code-review` - Revisión automática de código
- `/security-audit` - Auditoría de seguridad
- `/performance-check` - Análisis de performance
- `/release-notes` - Generar release notes
