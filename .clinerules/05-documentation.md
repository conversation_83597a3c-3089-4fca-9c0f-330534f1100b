# 05 - Documentación

## Directrices de Documentación

*   **Uso del Memory Bank**: Mantener y actualizar regularmente los archivos del `memory-bank/` (`projectbrief.md`, `productContext.md`, `activeContext.md`, `systemPatterns.md`, `techContext.md`, `progress.md`). Estos documentos son cruciales para el contexto del proyecto y la continuidad del trabajo.
*   **Documentación en Código**:
    *   Todas las funciones, clases, interfaces y tipos deben tener comentarios claros y concisos (preferiblemente usando JSDoc/TSDoc) que describan su propósito, parámetros, valores de retorno y cualquier efecto secundario.
    *   Explicar la lógica compleja o las decisiones de diseño no obvias con comentarios en línea.
*   **READMEs**: Cada directorio principal o módulo significativo debe contener un archivo `README.md` que describa su propósito, cómo usarlo, cómo probarlo y cualquier otra información relevante para su mantenimiento.
*   **Documentación de Arquitectura**: Mantener un registro de las decisiones de arquitectura importantes (ADRs - Architecture Decision Records) en un directorio específico (ej. `docs/adr/`).
*   **Documentación de APIs**: Documentar las APIs internas y externas, incluyendo endpoints, formatos de solicitud/respuesta, autenticación y ejemplos de uso.
*   **Documentación de Stack Tecnológico**: Mantener un registro detallado de las versiones exactas de todas las tecnologías y dependencias del proyecto, priorizando versiones LTS.
*   **Diagramas**: Utilizar diagramas (ej. diagramas de flujo, diagramas de componentes, diagramas de secuencia) para ilustrar arquitecturas complejas o flujos de trabajo.

## Herramientas de Documentación

*   **Generadores de Documentación**: Considerar el uso de herramientas como TypeDoc para generar documentación automáticamente a partir de los comentarios en el código.
*   **Herramientas de Diagramación**: Utilizar herramientas que permitan la creación de diagramas como código (ej. Mermaid, PlantUML) para facilitar su mantenimiento y versionado.

## Debe cubrir:

*   **JSDoc para funciones públicas**
*   **README.md structure**
*   **Storybook para componentes**
*   **API documentation (OpenAPI/Swagger)**
*   **Architecture Decision Records (ADRs)**
*   **Onboarding documentation**
